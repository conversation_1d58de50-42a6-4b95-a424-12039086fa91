import type { LuckysheetsSheet, LuckysheetsCell, ExtractedData, ExtractionResult } from './types';

/**
 * Luckysheet 工具类
 */
export class LuckysheetsUtils {
  /**
   * 列号转换为字母
   * @param colIndex 列索引（从0开始）
   * @returns 列字母
   */
  static columnIndexToLetter(colIndex: number): string {
    let result = '';
    while (colIndex >= 0) {
      result = String.fromCharCode((colIndex % 26) + 65) + result;
      colIndex = Math.floor(colIndex / 26) - 1;
    }
    return result;
  }

  /**
   * 字母转换为列号
   * @param letter 列字母
   * @returns 列索引（从0开始）
   */
  static columnLetterToIndex(letter: string): number {
    let result = 0;
    for (let i = 0; i < letter.length; i++) {
      result = result * 26 + (letter.charCodeAt(i) - 64);
    }
    return result - 1;
  }

  /**
   * 获取单元格值
   * @param sheet Sheet数据
   * @param row 行号（从0开始）
   * @param col 列号（从0开始）
   * @returns 单元格值
   */
  static getCellValue(sheet: LuckysheetsSheet, row: number, col: number): any {
    if (!sheet.celldata) return null;
    
    const cell = sheet.celldata.find(cell => cell.r === row && cell.c === col);
    return cell?.v?.v || null;
  }

  /**
   * 设置单元格值
   * @param sheet Sheet数据
   * @param row 行号（从0开始）
   * @param col 列号（从0开始）
   * @param value 值
   */
  static setCellValue(sheet: LuckysheetsSheet, row: number, col: number, value: any): void {
    if (!sheet.celldata) {
      sheet.celldata = [];
    }

    const existingCellIndex = sheet.celldata.findIndex(cell => cell.r === row && cell.c === col);
    
    if (existingCellIndex >= 0) {
      // 更新现有单元格
      sheet.celldata[existingCellIndex].v.v = value;
      sheet.celldata[existingCellIndex].v.m = String(value);
    } else {
      // 创建新单元格
      const newCell: LuckysheetsCell = {
        r: row,
        c: col,
        v: {
          v: value,
          m: String(value),
        },
      };
      sheet.celldata.push(newCell);
    }
  }

  /**
   * 获取指定范围的数据
   * @param sheet Sheet数据
   * @param startRow 起始行
   * @param endRow 结束行
   * @param startCol 起始列
   * @param endCol 结束列
   * @returns 范围数据
   */
  static getRangeData(
    sheet: LuckysheetsSheet,
    startRow: number,
    endRow: number,
    startCol: number,
    endCol: number
  ): any[][] {
    const result: any[][] = [];
    
    for (let row = startRow; row <= endRow; row++) {
      const rowData: any[] = [];
      for (let col = startCol; col <= endCol; col++) {
        rowData.push(this.getCellValue(sheet, row, col));
      }
      result.push(rowData);
    }
    
    return result;
  }

  /**
   * 获取列的唯一值
   * @param sheet Sheet数据
   * @param colIndex 列索引
   * @param startRow 起始行
   * @param endRow 结束行
   * @returns 唯一值数组
   */
  static getUniqueValues(
    sheet: LuckysheetsSheet,
    colIndex: number,
    startRow: number,
    endRow: number
  ): string[] {
    const values = new Set<string>();
    
    for (let row = startRow; row <= endRow; row++) {
      const value = this.getCellValue(sheet, row, colIndex);
      if (value !== null && value !== undefined && value !== '') {
        values.add(String(value));
      }
    }
    
    return Array.from(values);
  }

  /**
   * 根据唯一值提取数据
   * @param sheet Sheet数据
   * @param uniqueValues 唯一值列表
   * @param uniqueColumn 唯一值列索引
   * @param startRow 数据起始行
   * @param endRow 数据结束行
   * @param startCol 数据起始列
   * @param endCol 数据结束列
   * @param summaryColumns 汇总列配置
   * @returns 提取结果
   */
  static extractDataByUniqueValues(
    sheet: LuckysheetsSheet,
    uniqueValues: string[],
    uniqueColumn: number,
    startRow: number,
    endRow: number,
    startCol: number,
    endCol: number,
    summaryColumns?: Array<{ column: number; type: 'SUM' | 'COUNT' | 'AVG' | 'MAX' | 'MIN' }>
  ): ExtractionResult {
    const extractedData: ExtractedData[] = [];
    const newSheetData: LuckysheetsSheet = {
      name: '提取结果',
      color: '',
      index: '1',
      status: '1',
      order: '1',
      hide: 0,
      row: 1000,
      column: endCol - startCol + 1,
      defaultRowHeight: 19,
      defaultColWidth: 73,
      celldata: [],
      config: {},
      scrollLeft: 0,
      scrollTop: 0,
      luckysheet_select_save: [],
      calcChain: [],
      isPivotTable: false,
      pivotTable: {},
      filter_select: {},
      filter: {},
      luckysheet_alternateformat_save: [],
      luckysheet_alternateformat_save_modelCustom: [],
      luckysheet_conditionformat_save: [],
      frozen: {},
      chart: [],
      zoomRatio: 1,
      image: [],
      showGridLines: 1,
      dataVerification: {},
    };

    let currentRow = 0;

    // 复制表头
    for (let col = startCol; col <= endCol; col++) {
      const headerValue = this.getCellValue(sheet, startRow, col);
      this.setCellValue(newSheetData, currentRow, col - startCol, headerValue);
    }
    currentRow++;

    // 处理每个唯一值
    for (const uniqueValue of uniqueValues) {
      const matchingRows: any[] = [];
      
      // 添加唯一值行
      this.setCellValue(newSheetData, currentRow, 0, uniqueValue);
      currentRow++;

      // 查找匹配的行
      for (let row = startRow + 1; row <= endRow; row++) {
        const cellValue = this.getCellValue(sheet, row, uniqueColumn);
        if (String(cellValue) === uniqueValue) {
          const rowData: any[] = [];
          for (let col = startCol; col <= endCol; col++) {
            const value = this.getCellValue(sheet, row, col);
            rowData.push(value);
            this.setCellValue(newSheetData, currentRow, col - startCol, value);
          }
          matchingRows.push(rowData);
          currentRow++;
        }
      }

      // 计算汇总
      const summary: { [column: string]: number } = {};
      if (summaryColumns && summaryColumns.length > 0) {
        for (const summaryCol of summaryColumns) {
          const colIndex = summaryCol.column - startCol;
          const values = matchingRows
            .map(row => row[colIndex])
            .filter(val => val !== null && val !== undefined && !isNaN(Number(val)))
            .map(val => Number(val));

          let summaryValue = 0;
          switch (summaryCol.type) {
            case 'SUM':
              summaryValue = values.reduce((sum, val) => sum + val, 0);
              break;
            case 'COUNT':
              summaryValue = values.length;
              break;
            case 'AVG':
              summaryValue = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
              break;
            case 'MAX':
              summaryValue = values.length > 0 ? Math.max(...values) : 0;
              break;
            case 'MIN':
              summaryValue = values.length > 0 ? Math.min(...values) : 0;
              break;
          }

          summary[this.columnIndexToLetter(summaryCol.column)] = summaryValue;
          this.setCellValue(newSheetData, currentRow, colIndex, summaryValue);
        }
        currentRow++;
      }

      extractedData.push({
        uniqueValue,
        rows: matchingRows,
        summary,
      });

      // 添加空行分隔
      currentRow++;
    }

    return {
      extractedData,
      newSheetData,
    };
  }

  /**
   * 创建空的 Sheet
   * @param name Sheet名称
   * @param index Sheet索引
   * @returns Sheet数据
   */
  static createEmptySheet(name: string, index: string): LuckysheetsSheet {
    return {
      name,
      color: '',
      index,
      status: '1',
      order: index,
      hide: 0,
      row: 84,
      column: 60,
      defaultRowHeight: 19,
      defaultColWidth: 73,
      celldata: [],
      config: {},
      scrollLeft: 0,
      scrollTop: 0,
      luckysheet_select_save: [],
      calcChain: [],
      isPivotTable: false,
      pivotTable: {},
      filter_select: {},
      filter: {},
      luckysheet_alternateformat_save: [],
      luckysheet_alternateformat_save_modelCustom: [],
      luckysheet_conditionformat_save: [],
      frozen: {},
      chart: [],
      zoomRatio: 1,
      image: [],
      showGridLines: 1,
      dataVerification: {},
    };
  }

  /**
   * 将 Excel 数据转换为 Luckysheet 格式
   * @param excelData Excel数据
   * @returns Luckysheet格式数据
   */
  static convertExcelToLuckysheet(excelData: any[][]): LuckysheetsSheet {
    const sheet = this.createEmptySheet('Sheet1', '0');
    
    for (let row = 0; row < excelData.length; row++) {
      for (let col = 0; col < excelData[row].length; col++) {
        const value = excelData[row][col];
        if (value !== null && value !== undefined) {
          this.setCellValue(sheet, row, col, value);
        }
      }
    }
    
    return sheet;
  }

  /**
   * 将 Luckysheet 数据转换为 Excel 格式
   * @param sheet Sheet数据
   * @returns Excel格式数据
   */
  static convertLuckysheetToExcel(sheet: LuckysheetsSheet): any[][] {
    const result: any[][] = [];
    const maxRow = Math.max(...sheet.celldata.map(cell => cell.r), 0);
    const maxCol = Math.max(...sheet.celldata.map(cell => cell.c), 0);
    
    for (let row = 0; row <= maxRow; row++) {
      const rowData: any[] = [];
      for (let col = 0; col <= maxCol; col++) {
        rowData.push(this.getCellValue(sheet, row, col) || '');
      }
      result.push(rowData);
    }
    
    return result;
  }
}
