<template>
  <div ref="luckysheetsContainer" :id="containerId" class="luckysheet-container"></div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import { loadLuckysheet } from '/@/utils/luckysheet-init';

  interface Props {
    data?: any[];
    options?: any;
    height?: string | number;
    width?: string | number;
    readonly?: boolean;
    containerId?: string;
  }

  interface Emits {
    (e: 'change', data: any[]): void;
    (e: 'cellUpdated', data: any): void;
    (e: 'sheetActivate', data: any): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    data: () => [],
    options: () => ({}),
    height: '600px',
    width: '100%',
    readonly: false,
    containerId: () => `luckysheet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  });

  const emit = defineEmits<Emits>();

  const luckysheetsContainer = ref<HTMLElement>();
  let luckysheetsInstance: any = null;

  // 初始化 Luckysheet
  const initLuckysheetsInstance = async () => {
    try {
      return await loadLuckysheet();
    } catch (error) {
      console.error('Failed to load Luckysheet:', error);
      message.error('加载表格组件失败');
      return null;
    }
  };

  // 初始化 Luckysheet
  const initLuckysheet = async () => {
    const luckysheet = await initLuckysheetsInstance();
    if (!luckysheet || !luckysheetsContainer.value) return;

    try {
      // 设置容器样式
      if (luckysheetsContainer.value) {
        luckysheetsContainer.value.style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
        luckysheetsContainer.value.style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
      }

      // 默认配置
      const defaultOptions = {
        container: props.containerId,
        title: '电缆册',
        lang: 'zh',
        allowCopy: !props.readonly,
        allowEdit: !props.readonly,
        enableAddRow: !props.readonly,
        enableAddCol: !props.readonly,
        showsheetbar: true,
        showstatisticBar: true,
        sheetBottomConfig: false,
        allowUpdate: !props.readonly,
        functionButton: props.readonly ? '' : '<button id="luckysheet-share-btn-title">保存</button>',
        data: props.data && props.data.length > 0 ? props.data : [
          {
            name: 'Sheet1',
            color: '',
            index: '0',
            status: '1',
            order: '0',
            hide: 0,
            row: 84,
            column: 60,
            defaultRowHeight: 19,
            defaultColWidth: 73,
            celldata: [],
            config: {},
            scrollLeft: 0,
            scrollTop: 0,
            luckysheet_select_save: [],
            calcChain: [],
            isPivotTable: false,
            pivotTable: {},
            filter_select: {},
            filter: {},
            luckysheet_alternateformat_save: [],
            luckysheet_alternateformat_save_modelCustom: [],
            luckysheet_conditionformat_save: [],
            frozen: {},
            chart: [],
            zoomRatio: 1,
            image: [],
            showGridLines: 1,
            dataVerification: {},
          },
        ],
        hook: {
          cellUpdated: (r: number, c: number, oldValue: any, newValue: any, isRefresh: boolean) => {
            if (!isRefresh) {
              emit('cellUpdated', { row: r, col: c, oldValue, newValue });
              emit('change', luckysheet.getAllSheets());
            }
          },
          sheetActivate: (index: number, isPivotInitial: boolean, isNewSheet: boolean) => {
            emit('sheetActivate', { index, isPivotInitial, isNewSheet });
          },
          updated: () => {
            emit('change', luckysheet.getAllSheets());
          },
        },
      };

      // 合并用户配置
      const finalOptions = { ...defaultOptions, ...props.options };

      // 初始化 Luckysheet
      luckysheet.create(finalOptions);
      luckysheetsInstance = luckysheet;

      // 添加保存按钮事件监听
      nextTick(() => {
        const saveBtn = document.getElementById('luckysheet-share-btn-title');
        if (saveBtn) {
          saveBtn.addEventListener('click', () => {
            emit('change', luckysheet.getAllSheets());
            message.success('数据已保存');
          });
        }
      });
    } catch (error) {
      console.error('Failed to initialize Luckysheet:', error);
      message.error('初始化表格失败');
    }
  };

  // 获取数据
  const getData = () => {
    if (luckysheetsInstance) {
      return luckysheetsInstance.getAllSheets();
    }
    return [];
  };

  // 设置数据
  const setData = (data: any[]) => {
    if (luckysheetsInstance && data) {
      luckysheetsInstance.loadUrl(data);
    }
  };

  // 销毁实例
  const destroy = () => {
    if (luckysheetsInstance) {
      try {
        luckysheetsInstance.destroy();
        luckysheetsInstance = null;
      } catch (error) {
        console.error('Failed to destroy Luckysheet:', error);
      }
    }
  };

  // 监听数据变化
  watch(
    () => props.data,
    (newData) => {
      if (newData && luckysheetsInstance) {
        setData(newData);
      }
    },
    { deep: true }
  );

  // 监听只读状态变化
  watch(
    () => props.readonly,
    (readonly) => {
      if (luckysheetsInstance) {
        // 重新初始化以应用只读状态
        destroy();
        nextTick(() => {
          initLuckysheet();
        });
      }
    }
  );

  onMounted(() => {
    nextTick(() => {
      initLuckysheet();
    });
  });

  onUnmounted(() => {
    destroy();
  });

  // 暴露方法给父组件
  defineExpose({
    getData,
    setData,
    destroy,
    getInstance: () => luckysheetsInstance,
  });
</script>

<style scoped>
  .luckysheet-container {
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
  }

  /* 覆盖 Luckysheet 的一些默认样式 */
  :deep(.luckysheet-wa-editor) {
    z-index: 1000 !important;
  }

  :deep(.luckysheet-modal) {
    z-index: 2000 !important;
  }

  :deep(.luckysheet-tooltip-main) {
    z-index: 1500 !important;
  }
</style>
