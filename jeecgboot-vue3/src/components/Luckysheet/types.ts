export interface LuckysheetsCell {
  r: number; // 行号
  c: number; // 列号
  v: {
    v: any; // 原始值
    ct?: {
      fa: string; // 格式
      t: string; // 类型
    };
    m?: string; // 显示值
    f?: string; // 公式
    bg?: string; // 背景色
    fc?: string; // 字体颜色
    bl?: number; // 粗体
    it?: number; // 斜体
    fs?: number; // 字体大小
    ff?: string; // 字体
    un?: number; // 下划线
    st?: number; // 删除线
    ht?: number; // 水平对齐
    vt?: number; // 垂直对齐
    tb?: number; // 文本换行
    ps?: {
      // 边框
      l?: { style: number; color: string };
      r?: { style: number; color: string };
      t?: { style: number; color: string };
      b?: { style: number; color: string };
    };
  };
}

export interface LuckysheetsSheet {
  name: string; // sheet名称
  color?: string; // sheet颜色
  index: string; // sheet索引
  status: string; // sheet状态
  order: string; // sheet顺序
  hide: number; // 是否隐藏
  row: number; // 行数
  column: number; // 列数
  defaultRowHeight: number; // 默认行高
  defaultColWidth: number; // 默认列宽
  celldata: LuckysheetsCell[]; // 单元格数据
  config: {
    merge?: any; // 合并单元格
    rowlen?: any; // 行高
    columnlen?: any; // 列宽
    rowhidden?: any; // 隐藏行
    colhidden?: any; // 隐藏列
    borderInfo?: any; // 边框信息
    authority?: any; // 权限
  };
  scrollLeft: number; // 水平滚动位置
  scrollTop: number; // 垂直滚动位置
  luckysheet_select_save: any[]; // 选择区域
  calcChain: any[]; // 计算链
  isPivotTable: boolean; // 是否透视表
  pivotTable: any; // 透视表配置
  filter_select: any; // 筛选选择
  filter: any; // 筛选配置
  luckysheet_alternateformat_save: any[]; // 交替格式
  luckysheet_alternateformat_save_modelCustom: any[]; // 自定义交替格式
  luckysheet_conditionformat_save: any[]; // 条件格式
  frozen: any; // 冻结
  chart: any[]; // 图表
  zoomRatio: number; // 缩放比例
  image: any[]; // 图片
  showGridLines: number; // 显示网格线
  dataVerification: any; // 数据验证
}

export interface LuckysheetsOptions {
  container?: string; // 容器ID
  title?: string; // 标题
  lang?: string; // 语言
  allowCopy?: boolean; // 允许复制
  allowEdit?: boolean; // 允许编辑
  enableAddRow?: boolean; // 允许添加行
  enableAddCol?: boolean; // 允许添加列
  showsheetbar?: boolean; // 显示sheet栏
  showstatisticBar?: boolean; // 显示统计栏
  sheetBottomConfig?: boolean; // sheet底部配置
  allowUpdate?: boolean; // 允许更新
  functionButton?: string; // 功能按钮
  data?: LuckysheetsSheet[]; // 数据
  hook?: {
    cellUpdated?: (r: number, c: number, oldValue: any, newValue: any, isRefresh: boolean) => void;
    sheetActivate?: (index: number, isPivotInitial: boolean, isNewSheet: boolean) => void;
    updated?: () => void;
    [key: string]: any;
  };
}

export interface LuckysheetsInstance {
  create: (options: LuckysheetsOptions) => void;
  destroy: () => void;
  getAllSheets: () => LuckysheetsSheet[];
  loadUrl: (data: LuckysheetsSheet[]) => void;
  getSheet: (index?: number) => LuckysheetsSheet;
  setSheet: (data: LuckysheetsSheet, index?: number) => void;
  getCellValue: (row: number, col: number, sheetIndex?: number) => any;
  setCellValue: (row: number, col: number, value: any, sheetIndex?: number) => void;
  getRange: () => any;
  setRange: (range: any) => void;
  [key: string]: any;
}

// 数据提取相关类型
export interface ExtractedData {
  uniqueValue: string;
  rows: any[];
  summary?: { [column: string]: number };
}

export interface ExtractionResult {
  extractedData: ExtractedData[];
  newSheetData: LuckysheetsSheet;
}
