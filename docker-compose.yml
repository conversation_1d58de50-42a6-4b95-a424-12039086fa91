version: '2'
services:
  jeecg-boot-mysql:
    build:
      context: ./jeecg-boot/db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_ROOT_HOST: '%'
      TZ: Asia/Shanghai
    restart: always
    container_name: jeecg-boot-mysql
    image: jeecg-boot-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --default-authentication-plugin=caching_sha2_password
    ports:
      - 13306:3306
    networks:
      - jeecg-boot

  jeecg-boot-redis:
    image: registry.cn-hangzhou.aliyuncs.com/jeecgdocker/redis:5.0
    # ports:
    #   - 3792:6379
    restart: always
    hostname: jeecg-boot-redis
    container_name: jeecg-boot-redis
    networks:
      - jeecg-boot

  jeecg-boot-pgvector:
    image: registry.cn-hangzhou.aliyuncs.com/jeecgdocker/pgvector
    container_name: jeecg-boot-pgvector
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: vector_db
    ports:
      - 5432:5432
    restart: always
    networks:
      - jeecg-boot
      
  jeecg-boot-system:
    build:
      context: ./jeecg-boot/jeecg-module-system/jeecg-system-start
    restart: on-failure
    depends_on:
      - jeecg-boot-mysql
      - jeecg-boot-redis
    container_name: jeecg-boot-system
    image: jeecg-boot-system
    hostname: jeecg-boot-system
    ports:
      - 8080:8080
    networks:
      - jeecg-boot
  jeecg-vue:
    build:
      context: ./jeecgboot-vue3
    container_name: jeecgboot-vue3-nginx
    image: jeecgboot-vue3
    depends_on:
      - jeecg-boot-system
    networks:
      - jeecg-boot
    ports:
      - 80:80

networks:
  jeecg-boot:
    name: jeecg_boot
